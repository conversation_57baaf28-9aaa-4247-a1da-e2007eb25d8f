import { useCallback } from 'react';
import { _ } from 'utils/lodash';
import { actions as commonActions } from 'pages/common/slice';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';

const useScrollToError = () => {
  const scrollToFirstError = useCallback((errors) => {
    if (errors) {
      const firstErrorField = _.head(_.keys(errors));
      const errorElement = document.querySelector(`[name="${firstErrorField}"]`);

      if (errorElement && typeof errorElement.scrollIntoView === 'function') {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        errorElement.focus?.();
      }
    }
  }, []);

  return scrollToFirstError;
};

const useCustomToast = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const showErrorToast = useCallback((key, variant = 'warning') => {
    dispatch(commonActions.setCustomToast({
      open: true,
      variant,
      message: t(key),
      title: ''
    }));
  }, [dispatch, t]);

  return showErrorToast;
};

export { useScrollToError, useCustomToast };
