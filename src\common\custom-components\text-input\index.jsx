import {
  Input,
  InputGroup,
  InputRightElement,
  InputLeftElement
} from 'common/components';
import React from 'react';
import { useCustomToast } from 'utils/hooks';
import { restrictMalayalamChars } from 'utils/inputHelpers';
import ErrorText from '../error-text';
import FormLabel from '../form-label';

const TextInput = (props) => {
  const {
    name,
    label,
    error,
    disabled,
    readOnly,
    required,
    ellipsis,
    rightContent,
    leftContent,
    value,
    type = 'text',
    allowMalayalam = false,
    onInput,
    ...rest
  } = props;

  const showToast = useCustomToast();

  // Handle input events with Malayalam character restriction
  const handleInput = (e) => {
    // Only restrict Malayalam characters if allowMalayalam is false
    if (!allowMalayalam) {
      restrictMalayalamChars(e, showToast);
    }

    // Call the original onInput handler if provided
    if (onInput) {
      onInput(e);
    }
  };

  return (
    <div className="input__container">
      {label && (
        <FormLabel
          disabled={disabled || readOnly}
          label={label}
          required={required}
          ellipsis={ellipsis}
        />
      )}

      <InputGroup className={leftContent ? 'has-left-icon' : ''}>
        {leftContent && (
          <InputLeftElement
            className="custom-left-input-content"
            pointerEvents="none"
          >
            {leftContent}
          </InputLeftElement>
        )}
        <Input
          className={error && !(disabled || readOnly) ? 'error' : ''}
          variant="unstyled"
          type={type}
          id={name}
          name={name}
          disabled={disabled}
          readOnly={readOnly}
          value={[null, undefined].includes(value) ? '' : value}
          onInput={handleInput}
          {...rest}
        />
        {rightContent && (
          <InputRightElement className="custom-right-input-content">
            {rightContent}
          </InputRightElement>
        )}
      </InputGroup>

      {!(disabled || readOnly) && error && <ErrorText error={error} />}
    </div>
  );
};

export default TextInput;
