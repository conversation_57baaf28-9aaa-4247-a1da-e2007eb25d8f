import {
  <PERSON>, <PERSON><PERSON>, Card, Text, VStack, Heading,
  ChakraLink
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { ROUTE_URL } from 'common';
import { useResendOTPMutation, useResetPasswordMutation } from '../api';
import { changePasswordSchema } from '../validate';

const ChangePassword = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
      otp: ''
    },
    resolver: yupResolver(changePasswordSchema),
    mode: 'all'
  });

  const location = useLocation();
  const contact = location.state?.contact;
  const UUID = location.state?.id;
  const [resetPassword] = useResetPasswordMutation();
  const [resendOTP] = useResendOTPMutation();

  const onSubmit = (data) => {
    const payload = {
      password: data.newPassword,
      confirmPassword: data.confirmPassword,
      emailOrMobile: contact,
      otp: data.otp,
      id: UUID
    };
    resetPassword(payload);
  };

  const handleResendOTP = () => {
    if (contact) {
      resendOTP(contact);
    }
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={2} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={1} textAlign="center">
          <Heading fontSize="3xl" fontWeight="bold" color="gray.700">
            {t('changePassword')}
          </Heading>
          <Text fontSize="md" color="gray.500">
            {t('createNewPassword')}
          </Text>
        </VStack>

        <VStack spacing={6} w="full" mt={8}>
          <FormController
            type="password"
            name="newPassword"
            control={control}
            label={t('newPassword')}
            placeholder={t('fieldEnter', { field: t('newPassword') })}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="password"
            name="confirmPassword"
            control={control}
            label={t('confirmPassword')}
            placeholder={t('fieldEnter', { field: t('confirmPassword') })}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="otp"
            name="otp"
            control={control}
            label={t('verificationCode')}
            errors={errors}
            placeholder="XXXXXX"
            maxLength={6}
          />
        </VStack>

        <Text fontSize="sm" color="gray.500" textAlign="center" mt={4}>
          {t('otpSentMessage')}
        </Text>

        <Button
          variant="link"
          color="primary.A100"
          fontSize="sm"
          onClick={handleResendOTP}
          _hover={{ textDecoration: 'underline' }}
        >
          {t('resendCode')}
        </Button>

        <Button
          type="submit"
          variant="secondary"
          w="full"
          mt={{ base: 3, md: 6 }}
          isLoading={isSubmitting}
          loadingText={t('updating')}
          height="50px"
          fontSize="md"
        >
          {t('updatePassword')}
        </Button>

        <Text fontSize="md" color="gray.500" textAlign="center">
          <ChakraLink href={ROUTE_URL.AUTH.FULL_PATHS.LOGIN} color="secondary.500">
            {t('backToLogin')}
          </ChakraLink>
        </Text>
      </VStack>
    </Box>
  );
};

export default ChangePassword;
