import React, { useState, useRef, useEffect } from 'react';
import {
  Box, Text, Button, VStack, HStack, Input, Icon, Flex, t, CircularProgress,
  useDisclosure
} from 'common/components';
import { UploadIcon } from 'assets/svg';
import { FILE_ACCEPT_TYPE, FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';
import FileCard from './FilePreviewCard';
import DocumentModal from '../document-preview/DocumentModal';

const DocumentUploadCard = ({
  label = 'File Upload',
  name = 'fileUpload',
  required = false,
  description,
  fileFormat = t('fileFormatRequirements'),
  onFileUpload = () => {},
  onFileRemove = () => {},
  onFilePreview = () => {},
  previewData = [],
  showPreview = false,
  error = '',
  isMulti = false,
  isLoading = false,
  disabled = false
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState([]);
  const [fullView, setFullView] = useState({});
  const fileInputRef = useRef(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleFileSelect = (file) => {
    if (!file) return;
    if (onFileUpload) {
      onFileUpload(file);
    }
    if (file.size < FILE_SIZE && VALID_FILE_TYPE.includes(file?.type)) {
      setSelectedFile(file);
      const { type, size, name: fileName } = file || {};
      const objectUrl = URL.createObjectURL(file);
      if (!showPreview) {
        if (isMulti) {
          setPreview([...preview, {
            id: preview.length + 1, url: objectUrl, ext: fileName?.split('.').pop(), type, size, name: fileName
          }]);
        }
        setPreview([{
          id: 1, url: objectUrl, ext: fileName?.split('.').pop(), type, size, name: fileName
        }]);
      }
    }
  };

  const handleInputChange = (event) => {
    const file = event.target.files[0];
    handleFileSelect(file);
  };

  const handleUploadClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleFileDelete = (file) => {
    setPreview((prev) => prev.filter((imgObj) => imgObj.id !== file.id));
    setSelectedFile(null);
    onFileRemove(file);
  };

  const handleFilePreview = (file) => {
    setFullView(file);
    onFilePreview(file);
    onOpen(true);
  };

  useEffect(() => {
    setPreview([...previewData]);
  }, [previewData.length]);

  return (
    <Box
      w="full"
      bg="gray.80"
      border={error ? '1px' : '2px'}
      borderRadius="10px"
      borderColor={error ? 'red.500' : 'gray.80'}
      py={2}
      px={4}
    >
      {/* Header Section */}
      <Flex align="flex-start" justify="space-between" alignItems="center" gap={4} direction={{ base: 'column', md: 'row' }}>
        <Box flex={1} name={name}>
          <HStack align="center" spacing={1} mb={2}>
            <Text fontSize="sm" fontWeight={600} color="primary.900">
              {label}
            </Text>
            {required && (
              <Text fontSize="lg" color="red.500">*</Text>
            )}
          </HStack>
          <Text fontSize="sm" color="gray.600" mb={1}>
            {description}
          </Text>
          <Text fontSize="12px" color="gray.500">
            {fileFormat}
          </Text>
        </Box>
        <Box display="flex" alignItems="center" justifyContent="end" flexShrink={0} width={{ md: 'auto', base: '100%' }}>
          {showPreview
            ? preview?.map((imgObj, index) => {
              const img = imgObj?.url || '';
              const keyIndex = img + index + new Date();

              if (isLoading) {
                return <CircularProgress key={keyIndex} size="25px" isIndeterminate color="primary.300" />;
              }
              return img ? (
                <FileCard
                  key={keyIndex}
                  file={imgObj}
                  handleFileDelete={handleFileDelete}
                  handleFilePreview={handleFilePreview}
                />
              ) : null;
            })
            : null}
          {(!showPreview || preview.length < 1) && (
            <Button
              onClick={handleUploadClick}
              rightIcon={<Icon as={UploadIcon} boxSize={5} />}
              variant="outline"
              size="md"
              colorScheme="gray"
              borderRadius="4px"
              paddingY={5}
              paddingX={2}
              borderColor={disabled ? 'gray.200' : 'primary.A80'}
              _hover={{ bg: disabled ? 'white' : 'primary.50', cursor: disabled ? 'no-drop' : 'pointer' }}
            >
              {selectedFile ? t('changeFile') : t('upload')}
            </Button>
          )}
        </Box>
      </Flex>

      <Input
        ref={fileInputRef}
        name={name}
        type="file"
        accept={FILE_ACCEPT_TYPE}
        onChange={handleInputChange}
        display="none"
      />

      {/* Success State */}
      {selectedFile && !showPreview && (
        <Box
          mt={4}
          p={4}
          bg="green.50"
          border="1px"
          borderColor="green.200"
          borderRadius="md"
        >
          <Flex align="center" justify="space-between">
            <VStack align="flex-start" spacing={1}>
              <Text fontSize="sm" fontWeight="medium" color="green.800">
                {t('fileUploaded')}
              </Text>
              <Text fontSize="xs" color="green.600">
                {selectedFile.name} • {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </Text>
            </VStack>
          </Flex>
        </Box>
      )}
      {!disabled && error && <Text mt={2} color="red.500" fontSize="0.75rem">{error}</Text>}
      <DocumentModal {...{
        onOpen, onClose, isOpen, fileData: fullView
      }}
      />
    </Box>
  );
};

export default DocumentUploadCard;
