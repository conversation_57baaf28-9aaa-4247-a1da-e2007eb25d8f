import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box, TitledCard, CustomAlert, t, FormController, Grid, CircularProgress
} from 'common/components';
import { CloudFailed, GuideLines } from 'assets/svg';
import { _ } from 'utils/lodash';
import { FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';
import { getFileContent } from 'pages/others/fileDownload/helpers';
import { StepperButtons } from 'common/custom-components';
import { useParams } from 'react-router-dom';
import { API_URL } from 'common';
import { useDownloadFileMutation } from 'pages/others/fileDownload/api';
import { RESPONSE_TYPE } from 'pages/others/fileDownload/constant';
import { translateKey } from 'utils/translate';
import { useScrollToError } from 'utils/hooks';
import { documentSchema } from '../validation/document';
import { useGetRequiredDocumentsQuery, useUploadFileMutation } from '../api';
import { DOCUMENT_META_DATA } from '../constants';

const Documents = ({
  isHSSApplicant = false,
  isPGApplicant = false,
  hasSpecialStatus = false,
  onNext,
  onPrevious
}) => {
  const { applicationId } = useParams();
  const scrollToError = useScrollToError();
  const {
    data: { payload: { attachments = [] } = {} } = {},
    isLoading, isError
  } = useGetRequiredDocumentsQuery(applicationId);
  const [downloadFile, { data = {} }] = useDownloadFileMutation();
  const [uploadFile] = useUploadFileMutation();

  const {
    control, setValue, handleSubmit, formState: { errors }
  } = useForm({
    mode: 'all',
    resolver: yupResolver(documentSchema(attachments)),
    defaultValues: {
      isHSSApplicant,
      isPGApplicant,
      hasSpecialStatus
    }
  });

  const [previewUrls, setPreviewUrls] = useState({});

  const handleFileUpload = (file, key) => {
    const { name = '' } = _.get(DOCUMENT_META_DATA, key, {});
    if (!file) return;
    if (name && file.size < FILE_SIZE && VALID_FILE_TYPE.includes(file?.type)) {
      setValue(name, file);
      setPreviewUrls((prev) => ({ ...prev, [name]: [getFileContent(file)] }));
      const formData = new FormData();
      formData.append('file', file);
      uploadFile({
        url: API_URL.APPLICATION.UPLOAD_FILE.replace('{document}', key).replace('{id}', applicationId),
        file: formData
      });
    }
  };

  const handleRemoveFile = (documentType) => {
    setValue(documentType, null);
    if (previewUrls[documentType]) {
      URL.revokeObjectURL(previewUrls[documentType]);
      setPreviewUrls((prev) => ({ ...prev, [documentType]: [] }));
    }
  };

  const formSubmit = (fileData) => {
    if (onNext && attachments.length > 0) {
      onNext(fileData);
    }
  };

  useEffect(() => {
    downloadFile({
      url: API_URL.APPLICATION.GET_DOCUMENTS.replace('{id}', applicationId),
      responseType: RESPONSE_TYPE.JSON
    });
  }, []);

  useEffect(() => {
    if (attachments.length === 0) return;

    const newPreview = {};
    attachments.forEach((key) => {
      const { name = '' } = _.get(DOCUMENT_META_DATA, key, {});
      if (name && data[`${name}Url`] && !_.isEmpty(data[`${name}Url`])) {
        newPreview[name] = [data[`${name}Url`]];
      } else {
        newPreview[name] = null;
      }
      setValue(name, newPreview[name], { shouldDirty: true });
      setPreviewUrls(newPreview);
    });
  }, [data, attachments, setValue]);

  return (
    <form onSubmit={handleSubmit(formSubmit, scrollToError)}>
      <TitledCard title={t('documentUpload')}>
        <Box p={6}>
          <Grid templateColumns="repeat(1, 1fr)" gap={4}>
            <Box mb={9}>
              <CustomAlert
                label={t('documentGuidelinesTitle')}
                message={t('documentGuidelinesMessage')}
                bg="orange.50"
                iconColor="orange.500"
                textColor="orange.700"
                icon={GuideLines}
              />
            </Box>
            {isLoading && <CircularProgress />}
            {!isLoading && isError && (
            <Box display="grid" justifyContent="center" textAlign="center" w="full" justifyItems="center">
              <CloudFailed width="150" height="150" />
              <p className="font-bold text-[20px] text-[#09327B] w-full">{t('pleaseTryAgain')}</p>
            </Box>
            )}

            {!isLoading && !isError && attachments.map((key) => {
              const { name = '', label, description = '' } = _.get(DOCUMENT_META_DATA, key, {});
              if (!name) return null;
              return (
                <FormController
                  required
                  showPreview
                  type="file"
                  errors={errors}
                  key={name}
                  control={control}
                  name={name}
                  label={translateKey(label)}
                  previewData={previewUrls[name] || []}
                  description={description ? t(description) : ''}
                  handleChange={(file) => handleFileUpload(file, key)}
                  onFileRemove={() => handleRemoveFile(name)}
                />
              );
            })}
          </Grid>
        </Box>
      </TitledCard>
      {/* Action Buttons */}
      <Box mt={6}>
        <StepperButtons
          currentStep={4}
          totalSteps={6}
          layout="space-between"
          onPrevious={onPrevious}
          nextButtonProps={{
            isLoading,
            loadingText: t('saving')
          }}
        />
      </Box>
    </form>
  );
};

export default Documents;
