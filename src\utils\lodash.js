import {
  forEach, get, head, isEmpty, set, values, has, startsWith, isEqual, indexOf, isArray, uniq,
  filter, find, isNumber, includes, isUndefined, remove, cloneDeep, omitBy, flow, orderBy, toString,
  isObject, last, isNull, endsWith, toLower, toUpper, capitalize, range, isString, pickBy, random,
  reverse, lowerCase, isNil, union, forIn, findIndex, isDate, keys
} from 'lodash-es';

export const _ = {
  toString,
  isArray,
  startsWith,
  endsWith,
  values,
  has,
  get,
  indexOf,
  forEach,
  isEmpty,
  head,
  set,
  isEqual,
  filter,
  find,
  isNumber,
  includes,
  isUndefined,
  remove,
  cloneDeep,
  omitBy,
  isObject,
  last,
  isNull,
  toLower,
  toUpper,
  capitalize,
  range,
  isString,
  pickBy,
  flow,
  random,
  orderBy,
  uniq,
  reverse,
  lowerCase,
  isNil,
  union,
  forIn,
  findIndex,
  isDate,
  keys
};
