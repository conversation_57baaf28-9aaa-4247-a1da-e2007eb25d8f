import React from 'react';
import {
  Box,
  Flex,
  Text,
  CircularProgress
} from '@chakra-ui/react';

const CircularLoader = ({
  message,
  fullPage = false,
  minHeight = { base: '300px', md: '400px' }
}) => {
  return (
    <Box
      bg={fullPage ? 'gray.50' : 'transparent'}
      w="100%"
    >
      <Flex
        direction="column"
        align="center"
        justify="center"
        gap={{ base: 3, md: 4 }}
        minH={fullPage ? '100vh' : minHeight}
        py={{ base: 4, md: 6 }}
        px={{ base: 4, md: 0 }}
        textAlign="center"
      >
        <CircularProgress
          isIndeterminate
          size={{ base: '36px', md: '48px' }}
          thickness="8px"
          trackColor="secondary.500"
          color="primary.500"
        />
        {message && (
          <Text
            fontSize={{ base: 'sm', md: 'md' }}
            color="gray.600"
            fontWeight="medium"
          >
            {message}
          </Text>
        )}
      </Flex>
    </Box>
  );
};

export default CircularLoader;
