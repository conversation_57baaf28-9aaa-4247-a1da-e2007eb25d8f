import {
  <PERSON>, Button, Card, ChakraLink, <PERSON><PERSON><PERSON>ck, Text, VStack
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { ROUTE_URL } from 'common';
import { useLoginUserMutation } from '../api';
import { loginSchema } from '../validate';

const Login = () => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      emailOrMobile: '',
      password: ''
    },
    resolver: yupResolver(loginSchema),
    mode: 'all'
  });

  const [loginUser, { isLoading }] = useLoginUserMutation();

  const onSubmit = (data) => {
    const credentials = {
      emailOrMobile: data.emailOrMobile,
      password: data.password
    };
    loginUser(credentials);
  };

  return (
    <Box
      mt={{ base: 4, md: '3rem' }}
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
    >
      <VStack spacing={8} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={4} textAlign="center">
          <Text fontSize="3xl" fontWeight="bold" color="gray.700">
            {t('welcomeBack')}
          </Text>
        </VStack>

        <VStack spacing={6} w="full" mt={12}>
          <FormController
            type="text"
            name="emailOrMobile"
            control={control}
            label={t('emailOrMobile')}
            placeholder={t('fieldEnter', { field: t('emailOrMobile') })}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="password"
            name="password"
            control={control}
            label={t('password')}
            placeholder={t('fieldEnter', { field: t('password') })}
            errors={errors}
            inputHeight="50px"
          />

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 12 }}
            isLoading={isLoading}
            loadingText={t('signingIn')}
          >
            {t('signIn')}
          </Button>

          <VStack spacing={1} fontSize="md" mt={4}>
            <HStack>
              <Text color="gray.500">{t('noAccount')}</Text>
              <ChakraLink href={ROUTE_URL.AUTH.FULL_PATHS.REGISTER} color="secondary.500">
                {t('createAccount')}
              </ChakraLink>
            </HStack>
            <ChakraLink href={ROUTE_URL.AUTH.FULL_PATHS.RESET_PASSWORD} color="secondary.500">
              {t('forgotPassword')}
            </ChakraLink>
          </VStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default Login;
