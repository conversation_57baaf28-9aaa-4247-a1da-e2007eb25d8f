import { createApi } from '@reduxjs/toolkit/query/react';
import { API_URL, ROUTE_URL } from 'common';
import { getBaseQuery, handleAPIRequest } from 'utils/http';
import { t } from 'i18next';
import { actions as commonActions } from 'pages/common/slice';
import { documentApi } from 'pages/others/fileDownload/api';
import { RESPONSE_TYPE } from 'pages/others/fileDownload/constant';
import { actions as applicationActions } from './slice';
import { STATE_REDUCER_KEY } from './constants';

export const applicationApi = createApi({
  reducerPath: STATE_REDUCER_KEY.API,
  baseQuery: getBaseQuery(),
  tagTypes: ['applications', 'personalDetails', 'bankDetails', 'academicDetails', 'parentDetails', 'applicationDetails', 'requiredDocuments', 'submitApplication', 'uploadDocument'],
  endpoints: (builder) => ({
    getApplications: builder.query({
      query: (params = {}) => {
        const {
          page = 0, size = 5, search = '', status = ''
        } = params;
        const queryParams = new URLSearchParams({
          page: page.toString(),
          size: size.toString(),
          search,
          status
        });
        return `${API_URL.APPLICATION.GET_APPLICATIONS}?${queryParams.toString()}`;
      },
      providesTags: ['applications']
    }),
    getApplicationDetails: builder.query({
      query: (id) => API_URL.APPLICATION.GET_APPLICATION_DETAILS.replace('{id}', id),
      providesTags: ['applicationDetails']
    }),
    savePersonalDetails: builder.mutation({
      query: (data) => ({
        url: API_URL.APPLICATION.SAVE_PERSONAL_DETAILS,
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['personalDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('applicantDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          if (applicationId) {
            dispatch(applicationActions.setApplicationId(applicationId));
            dispatch(commonActions.navigateTo({
              to: `ui/applicant/application/${applicationId}`,
              replace: true
            }));
          }
        }
      })
    }),
    updatePersonalDetails: builder.mutation({
      query: ({ id, ...data }) => ({
        url: API_URL.APPLICATION.UPDATE_PERSONAL_DETAILS.replace('{id}', id),
        method: 'PUT',
        body: data
      }),
      invalidatesTags: ['personalDetails', 'applicationDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('applicantDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          const applicationIdData = _arg.id || applicationId;
          if (applicationIdData) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationIdData, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    saveParentDetails: builder.mutation({
      query: (data) => ({
        url: API_URL.APPLICATION.SAVE_PARENT_DETAILS,
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['parentDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('parentGuardianDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId, id } = {} } = {} } = result;
          const applicationIdData = _arg.applicationId || applicationId;
          if (id) {
            dispatch(applicationActions.setFormId({ formType: 'parentGuardianId', id }));
          }
          if (applicationIdData) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationIdData, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    updateParentDetails: builder.mutation({
      query: ({ id, ...data }) => ({
        url: API_URL.APPLICATION.UPDATE_PARENT_DETAILS.replace('{id}', id),
        method: 'PUT',
        body: data
      }),
      invalidatesTags: ['parentDetails', 'applicationDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('parentGuardianDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId, id } = {} } = {} } = result;
          const applicationIdData = _arg.applicationId || applicationId;
          if (id) {
            dispatch(applicationActions.setFormId({ formType: 'parentGuardianId', id }));
          }
          if (applicationIdData) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationIdData, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    saveBankDetails: builder.mutation({
      query: (data) => ({
        url: API_URL.APPLICATION.SAVE_BANK_DETAILS,
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['bankDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('bankDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          if (applicationId) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationId, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    updateBankDetails: builder.mutation({
      query: ({ id, ...data }) => ({
        url: API_URL.APPLICATION.UPDATE_BANK_DETAILS.replace('{id}', id),
        method: 'PUT',
        body: data
      }),
      invalidatesTags: ['bankDetails', 'applicationDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('bankDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          const applicationIdData = _arg.applicationId || applicationId;
          if (applicationIdData) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationIdData, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    getBankDetails: builder.query({
      query: (applicationId) => ({
        url: `${API_URL.APPLICATION.GET_BANK_DETAILS}/${applicationId}`,
        method: 'GET'
      }),
      providesTags: ['bankDetails']
    }),
    getBoard: builder.query({
      query: (educationQualificationId) => ({
        url: `${API_URL.COMMON.GET_BOARD}/${educationQualificationId}`,
        method: 'GET'
      }),
      providesTags: ['academicDetails']
    }),

    saveAcademicDetails: builder.mutation({
      query: (data) => ({
        url: API_URL.APPLICATION.SAVE_ACADEMIC_DETAILS,
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['academicDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('academicDetails') }),
        errorMessage: t('errorMessage', { type: t('academicDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId, id } = {} } = {} } = result;
          if (id) {
            dispatch(applicationActions.setFormId({ formType: 'academicDetailsId', id }));
          }
          if (applicationId) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationId, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    getAcademicDetails: builder.query({
      query: (applicationId) => `${API_URL.APPLICATION.ACADEMIC_DETAILS}/${applicationId}`,
      transformResponse: (response) => {
        if (response.payload) {
          return {
            ...response.payload
          };
        }
        return response;
      },
      providesTags: ['academicDetails']
    }),
    updateAcademicDetails: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `${API_URL.APPLICATION.UPDATE_ACADEMIC_DETAILS}/${id}`,
        method: 'PUT',
        body: data
      }),
      invalidatesTags: ['academicDetails', 'applicationDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('academicDetails') }),
        errorMessage: t('errorMessage', { type: t('academicDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          const applicationIdData = _arg.applicationId || applicationId;
          if (applicationIdData) {
            dispatch(applicationApi.endpoints.getApplicationDetails.initiate(applicationIdData, {
              forceRefetch: true
            }));
          }
        }
      })
    }),

    getRequiredDocuments: builder.query({
      query: (applicationId) => API_URL.APPLICATION.REQUIRED_DOCUMENTS.replace('{id}', applicationId),
      providesTags: ['requiredDocuments']
    }),
    uploadFile: builder.mutation({
      query: ({ url, file }) => ({
        url, method: 'POST', body: file
      }),
      providesTags: ['uploadDocument'],
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { applicationId } = {} } = {} } = result;
          if (applicationId) {
            dispatch(documentApi.endpoints.downloadFile.initiate({
              url: API_URL.APPLICATION.GET_DOCUMENTS.replace('{id}', applicationId),
              responseType: RESPONSE_TYPE.JSON
            }, {
              forceRefetch: true
            }));
          }
        }
      })
    }),
    gradeOptions: builder.query({
      query: ({ educationQualificationId, boardId }) => ({
        url: `${API_URL.APPLICATION.GRADE_OPTIONS}`,
        method: 'GET',
        params: {
          educationQualificationId,
          boardId
        }
      })
    }),
    submitApplication: builder.mutation({
      query: ({ applicationId }) => ({
        url: API_URL.APPLICATION.SUBMIT_APPLICATION.replace('{id}', applicationId),
        method: 'POST'
      }),
      invalidatesTags: ['submitApplication'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('applicationSubmittedSuccessfully'),
        errorMessage: t('applicationSubmissionFailed'),
        onSuccess: (_result, { closeAlert }, dispatch) => {
          if (closeAlert) closeAlert();
          dispatch(commonActions.navigateTo({
            to: `ui/${ROUTE_URL.APPLICANT.BASE.DASHBOARD}`
          }));
        }
      })
    })
  })
});

export const {
  useGetApplicationsQuery,
  useSavePersonalDetailsMutation,
  useSaveAcademicDetailsMutation,
  useUpdateAcademicDetailsMutation,
  useGetAcademicDetailsQuery,
  useGetApplicationDetailsQuery,
  useUpdatePersonalDetailsMutation,
  useSaveParentDetailsMutation,
  useUpdateParentDetailsMutation,
  useSaveBankDetailsMutation,
  useUpdateBankDetailsMutation,
  useGetBankDetailsQuery,
  useGetBoardQuery,
  useUploadFileMutation,
  useGetRequiredDocumentsQuery,
  useSubmitApplicationMutation,
  useGradeOptionsQuery
} = applicationApi;
