import { Textarea } from 'common/components';
import React from 'react';
import { useCustomToast } from 'utils/hooks';
import { restrictMalayalamChars } from 'utils/inputHelpers';
import ErrorText from '../error-text';
import FormLabel from '../form-label';

const TextArea = (props) => {
  const {
    placeHolder, name, label, error, disabled, readOnly, required, size = 'md', style = { width: '100%', height: '100px', lineHeight: '30px' }, allowMalayalam = false, onInput, ...rest
  } = props;

  const showToast = useCustomToast();

  // Handle input events with Malayalam character restriction
  const handleInput = (e) => {
    // Only restrict Malayalam characters if allowMalayalam is false
    if (!allowMalayalam) {
      restrictMalayalamChars(e, showToast);
    }

    // Call the original onInput handler if provided
    if (onInput) {
      onInput(e);
    }
  };

  return (
    <div className="text-area__container">
      <FormLabel disabled={disabled || readOnly} label={label} required={required} />
      <Textarea
        placeholder={placeHolder}
        className={error && !disabled ? 'error' : ''}
        variant="unstyled"
        type="text"
        size={size}
        id={name}
        name={name}
        style={style}
        disabled={disabled || false}
        readOnly={readOnly || false}
        onInput={handleInput}
        {...rest}
      />
      {!disabled && error && <ErrorText error={error} />}
    </div>
  );
};

export default TextArea;
