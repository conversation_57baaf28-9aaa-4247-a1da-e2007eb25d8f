import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { BackForward } from 'assets/svg';
import {
  Box, Button, Flex, Heading, HStack, IconButton, Text,
  useColorModeValue, VStack, FormController, ChakraLink
} from 'common/components';
import { yupResolver } from '@hookform/resolvers/yup';
import { SIGN_UP_USER_TYPE, USER_APPLICANT } from 'pages/common/constant';
import { t } from 'i18next';
import { ROUTE_URL } from 'common';
import { useRegisterUserMutation } from '../api';
import { signUpSchema } from '../validate';

const RegistrationForm = () => {
  const {
    control, handleSubmit, formState: { errors }, watch, resetField
  } = useForm({
    defaultValues: {
      userName: '',
      emailId: '',
      officialId: '',
      mobileNumber: '',
      password: '',
      confirmPassword: '',
      userType: USER_APPLICANT.code
    },
    resolver: yupResolver(signUpSchema),
    mode: 'all'
  });

  const userType = watch('userType');
  const [registerUser, { isLoading }] = useRegisterUserMutation();

  useEffect(() => {
    resetField('officialId');
    resetField('mobileNumber');
    resetField('emailId');
  }, [userType, resetField]);

  const onSubmit = (data) => {
    const payload = {
      userName: data.userName,
      password: data.password,
      confirmPassword: data.confirmPassword,
      userType
    };

    if (userType === USER_APPLICANT.code) {
      payload.emailId = data.emailId;
      payload.mobileNumber = data.mobileNumber;
    } else {
      payload.officialId = data.officialId;
    }
    registerUser(payload);
  };

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box
      py={{ base: 0, md: 2 }}
      px={{ base: 4, md: '5rem' }}
      bg={{ base: 'transparent', md: bgColor }}
      borderRadius={{ base: 'none', md: '30px' }}
      boxShadow={{ base: 'none', md: 'xl' }}
      border={{ base: 'none', md: '1px' }}
      borderColor={{ base: 'transparent', md: borderColor }}
    >
      {/* Header */}
      <HStack mb={2} spacing={3} mt={5}>
        <IconButton
          icon={<BackForward />}
          variant="ghost"
          size="sm"
          aria-label="Go back"
          display={{ base: 'none', md: 'flex' }}
        />
        <VStack align="start" spacing={0}>
          <Heading fontSize={{ base: '18px', md: '20px' }} color="blue.600">
            {t('createAccount')}
          </Heading>
          <Text fontSize="xs" color="gray.600">
            {t('completeRegister')}
          </Text>
        </VStack>
      </HStack>

      <Flex mb={2} ml={{ base: 0, md: '50%' }}>
        <FormController
          type="radio"
          name="userType"
          control={control}
          options={SIGN_UP_USER_TYPE}
          optionKey="code"
          size={{ base: 'sm', md: 'md' }}
          spacing={{ base: 2, md: 4 }}
          fontSize={{ base: 'sm', md: 'md' }}
        />
      </Flex>

      {/* Form Fields */}
      <VStack spacing={6} as="form" onSubmit={handleSubmit(onSubmit)}>
        <FormController
          type="text"
          name="userName"
          control={control}
          label={t('fullName')}
          placeholder={t('fieldEnter', { field: t('fullName') })}
          errors={errors}
        />

        {userType === USER_APPLICANT.code ? (
          <>
            <FormController
              type="email"
              name="emailId"
              control={control}
              label={t('emailId')}
              placeholder={t('fieldEnter', { field: t('emailId') })}
              errors={errors}
            />
            <FormController
              type="tel"
              name="mobileNumber"
              maxLength={10}
              control={control}
              label={t('mobileNo')}
              placeholder={t('fieldEnter', { field: t('mobileNo') })}
              errors={errors}
            />
          </>
        ) : (
          <FormController
            type="text"
            name="officialId"
            control={control}
            label={t('norkaId')}
            placeholder={t('fieldEnter', { field: t('norkaId') })}
            errors={errors}
          />
        )}

        <FormController
          type="password"
          name="password"
          control={control}
          label={t('password')}
          placeholder={t('fieldEnter', { field: t('password') })}
          errors={errors}
        />

        <FormController
          type="password"
          name="confirmPassword"
          control={control}
          label={t('confirmPassword')}
          placeholder={t('fieldEnter', { field: t('confirmPassword') })}
          errors={errors}
        />

        <Button
          type="submit"
          w="full"
          variant="secondary"
          mt={3}
          isLoading={isLoading}
          loadingText="Registering..."
        >
          {t('nextButton')}
        </Button>
      </VStack>

      <Text textAlign="center" mt={2} fontSize="md" color="gray.600">
        {t('alreadyRegistered')}{'  '}
        <ChakraLink href={`/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.AUTH.BASE}/${ROUTE_URL.AUTH.LOGIN}`} color="secondary.500">
          {t('signInLink')}
        </ChakraLink>
      </Text>
    </Box>
  );
};

export default RegistrationForm;
