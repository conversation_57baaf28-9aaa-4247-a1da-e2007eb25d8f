// validation/bankDetails.js
import { t } from 'common/components';
import { NUMERIC } from 'common/regex';
import * as yup from 'yup';

export const bankDetailsSchema = yup.object().shape({
  applicationId: yup.string(),
  accountHolderName: yup
    .string()
    .required(t('fieldRequired', { field: t('accountHolderName') }))
    .min(5, t('fieldMinLength', { field: t('accountHolderName'), min: t('5') })),
  accountNumber: yup
    .string()
    .required(t('fieldRequired', { field: t('accountNumber') }))
    .matches(NUMERIC, t('onlyNumbersAllowed'))
    .min(9, t('fieldMinLength', { field: t('accountNumber'), min: t('9') }))
    .max(18, t('fieldMaxLength', { field: t('accountNumber'), max: t('18') })),

  district: yup
    .string()
    .required(t('fieldRequired', { field: t('district') })),
  bankId: yup
    .string()
    .required(t('fieldRequired', { field: t('bankName') })),
  branchId: yup
    .string()
    .required(t('fieldRequired', { field: t('branchName') })),
  ifscCode: yup
    .string()
    .required(t('fieldRequired', { field: t('ifscCode') }))
    .matches(/^[A-Za-z]{4}0[A-Za-z0-9]{6}$/, t('invalidIFSCFormat'))
    .uppercase()
});
