import {
  Box, Button, Card, HStack, Text, VStack, IconButton, Heading
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useLocation } from 'react-router-dom';
import { BackForward } from 'assets/svg';
import { yupResolver } from '@hookform/resolvers/yup';
import { t } from 'i18next';
import { ROUTE_URL } from 'common';
import { useResendOTPMutation, useVerifyOTPMutation } from '../api';
import { otpVerificationSchema } from '../validate';

const OTPVerificationPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      otp: ''
    },
    resolver: yupResolver(otpVerificationSchema),
    mode: 'onBlur'
  });
  const location = useLocation();
  const { id, mobileNumber, emailId } = location.state || {};

  const [verifyOTP] = useVerifyOTPMutation();
  const [resendOTP] = useResendOTPMutation();

  const onSubmit = (data) => {
    const payload = {
      id,
      otp: data.otp,
      mobileNumber
    };
    verifyOTP(payload);
  };

  const handleResendOTP = () => {
    if (mobileNumber) {
      resendOTP({ mobileNo: mobileNumber });
    }
  };

  const renderOTPMessage = () => {
    if (mobileNumber) {
      return t('otpSentToMobile', { number: mobileNumber.slice(-3) });
    }
    if (emailId) {
      return t('otpSentToEmail', { email: emailId });
    }
    return t('otpSentToContact');
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      display={{ base: 'block', md: 'grid' }}
      minH={{ md: '580px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full">
        <HStack mb={2} spacing={3} mt={5}>
          <IconButton
            icon={<BackForward />}
            variant="ghost"
            size="sm"
            aria-label={t('goBack')}
            display={{ base: 'none', md: 'flex' }}
          />
          <VStack align="start" spacing={0}>
            <Heading fontSize={{ base: '18px', md: '20px' }} color="blue.600">
              {t('createYourAccount')}
            </Heading>
            <Text fontSize="xs" color="gray.600">
              {t('registrationCompletionText')}
            </Text>
          </VStack>
        </HStack>

        <VStack spacing={6} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
          <Text fontSize="md" color="gray.500" textAlign="center">
            {renderOTPMessage()}
          </Text>

          <FormController
            name="otp"
            control={control}
            errors={errors}
            type="otp"
            label={t('enterField', { field: t('otp') })}
            placeholder="XXXXXX"
            maxLength={6}
          />

          <Button
            variant="link"
            color="primary.A100"
            fontSize="md"
            onClick={handleResendOTP}
            _hover={{ textDecoration: 'underline' }}
            disabled={isSubmitting}
          >
            {t('resendOTP')}
          </Button>

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 12 }}
            isLoading={isSubmitting}
            loadingText={t('verifyingText')}
          >
            {t('verify')}
          </Button>

          <Text fontSize="md" color="gray.500" textAlign="center" mt={6}>
            {t('alreadyRegisteredText')}{' '}
            <Link to={ROUTE_URL.AUTH.FULL_PATHS.LOGIN} style={{ color: 'primary.A100', textDecoration: 'underline' }}>
              {t('signInLink')}
            </Link>
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default OTPVerificationPage;
