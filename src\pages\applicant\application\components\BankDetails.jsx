import {
  Box,
  CustomAlert,
  FormController,
  Grid,
  GridItem,
  t,
  TitledCard
} from 'common/components';
import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { WarningIcon } from 'assets/svg';
import { StepperButtons } from 'common/custom-components';
import { useSelector } from 'react-redux';
import {
  useFetchBankQuery,
  useFetchBranchQuery,
  useFetchDistrictQuery
} from 'pages/common/api';
import { STATE } from 'common/constants';
import { useScrollToError } from 'utils/hooks';
import { bankDetailsSchema } from '../validation/bankDetails';
import {
  useSaveBankDetailsMutation,
  useUpdateBankDetailsMutation,
  useGetBankDetailsQuery
} from '../api';
import { getApplicationId } from '../selectors';
import { transformBankDetails } from '../helpers';

const BankDetails = ({ onNext, onPrevious }) => {
  const scrollToError = useScrollToError();
  const applicationId = useSelector(getApplicationId);

  const { data: bankDetailsData, isLoading: isBankDetailsLoading } = useGetBankDetailsQuery(
    applicationId,
    { skip: !applicationId }
  );
  const { data: bankDetails } = useFetchBankQuery();
  const { data: districtOptions = [] } = useFetchDistrictQuery(STATE.id);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    trigger
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(bankDetailsSchema)
  });

  const selectedBankId = watch('bankId');
  const selectedDistrictId = watch('district');
  const selectedBranchId = watch('branchId');
  const ifscCode = watch('ifscCode');

  const { data: branchDetails, isLoading: isBranchLoading } = useFetchBranchQuery(
    { bankId: selectedBankId, districtId: selectedDistrictId },
    { skip: !selectedBankId || !selectedDistrictId }
  );

  React.useEffect(() => {
    if (selectedBranchId && branchDetails?.payload) {
      const selectedBranch = branchDetails.payload.find(
        (branch) => branch.id === Number(selectedBranchId)
      );
      if (selectedBranch) {
        setValue('ifscCode', selectedBranch.ifscCode, { shouldValidate: true });
        trigger('ifscCode');
      }
    }
  }, [selectedBranchId, branchDetails, setValue, trigger]);

  React.useEffect(() => {
    const districtLoaded = districtOptions?.payload?.length > 0;
    const bankLoaded = bankDetails?.payload?.length > 0;
    if (applicationId)setValue('applicationId', applicationId);
    if (bankDetailsData?.payload && districtLoaded && bankLoaded) {
      const { payload } = bankDetailsData;
      const initialValues = {
        applicationId,
        accountHolderName: payload.accountHolderName,
        accountNumber: payload.accountNumber,
        ifscCode: payload.ifscCode,
        district: payload.district?.id || '',
        bankId: payload.bank?.id || '',
        branchId: payload.bankBranch?.id || ''
      };

      reset(initialValues);
      setValue('bankId', payload.bank?.id);
      setValue('district', payload.district?.id);
    }
  }, [bankDetailsData, districtOptions, bankDetails, applicationId, reset, setValue]);

  const [saveBankDetails, { isLoading: isSaveLoading }] = useSaveBankDetailsMutation();
  const [updateBankDetails, { isLoading: isUpdateLoading }] = useUpdateBankDetailsMutation();
  const isLoading = isSaveLoading || isUpdateLoading || isBankDetailsLoading;

  const onSubmit = async (data) => {
    const payload = transformBankDetails(data);
    if (bankDetailsData?.payload?.id) {
      await updateBankDetails({
        id: bankDetailsData.payload.id,
        ...payload
      }).unwrap();
    } else {
      await saveBankDetails(payload).unwrap();
    }
    onNext?.(data);
  };

  if (isBankDetailsLoading) {
    return <div>Loading bank details...</div>;
  }

  return (
    <form onSubmit={handleSubmit(onSubmit, scrollToError)}>
      <TitledCard title={t('bankDetails')}>
        <Box p={6}>
          <Box mb={10}>
            <CustomAlert
              title={t('importantNote')}
              message={t('bankAccountRequirement')}
              icon={WarningIcon}
              bg="orange.50"
              iconColor="orange.500"
              textColor="orange.700"
            />
          </Box>

          <Grid templateColumns="repeat(12, 1fr)" gap={6}>
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="text"
                label={t('accountHolderName')}
                name="accountHolderName"
                control={control}
                errors={errors}
                placeholder={t('enterField', { field: t('accountHolderName') })}
                required
              />
            </GridItem>

            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="number"
                label={t('accountNumber')}
                name="accountNumber"
                control={control}
                errors={errors}
                placeholder={t('fieldEnter', { field: t('accountNumber') })}
                required
                maxLength={18}
              />
            </GridItem>

            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('district')}
                name="district"
                control={control}
                errors={errors}
                options={districtOptions?.payload || []}
                optionKey="id"
                optionLabel="name"
                required
                placeholder={t('selectField', { field: t('district') })}
              />
            </GridItem>

            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('bankName')}
                name="bankId"
                control={control}
                errors={errors}
                options={bankDetails?.payload || []}
                lngOptions={{ en: 'bankName' }}
                optionKey="id"
                optionLabel="bankName"
                required
                placeholder={t('select')}
                isLoading={!bankDetails}
              />
            </GridItem>

            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('branchName')}
                name="branchId"
                lngOptions={{ en: 'branchName' }}
                control={control}
                errors={errors}
                options={branchDetails?.payload || []}
                optionKey="id"
                optionLabel="name"
                required
                placeholder={t('select')}
                isDisabled={!selectedBankId || !selectedDistrictId}
                isLoading={isBranchLoading}
              />
            </GridItem>

            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="text"
                label={t('ifscCode')}
                name="ifscCode"
                control={control}
                errors={errors}
                placeholder={t('fieldEnter', { field: t('ifsc') })}
                required
                readOnly
                value={ifscCode}
                transformInput={(value) => value?.toUpperCase()}
              />
            </GridItem>
          </Grid>
        </Box>
      </TitledCard>

      <Box mt={6}>
        <StepperButtons
          currentStep={4}
          totalSteps={6}
          onPrevious={onPrevious}
          layout="space-between"
          nextButtonProps={{
            isLoading,
            loadingText: t('saving')
          }}
        />
      </Box>
    </form>
  );
};

export default BankDetails;
